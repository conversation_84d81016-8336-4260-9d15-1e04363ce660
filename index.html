<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spaceship Top-Down Game</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        canvas {
            border: 2px solid #333;
            background-color: #111;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .controls h3 {
            margin-bottom: 10px;
        }
        
        .controls p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Spaceship Explorer</h1>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    
    <div class="controls">
        <h3>Controls:</h3>
        <p>W, A, S, D - Move character</p>
        <p>Walk near doors to open them automatically</p>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
