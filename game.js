// Game canvas and context
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game constants
const TILE_SIZE = 20;
const PLAYER_SIZE = 16;
const DOOR_OPEN_DISTANCE = 40;

// Player object
const player = {
    x: 100,
    y: 100,
    speed: 3,
    color: '#00ff00'
};

// Input handling
const keys = {};

document.addEventListener('keydown', (e) => {
    keys[e.key.toLowerCase()] = true;
});

document.addEventListener('keyup', (e) => {
    keys[e.key.toLowerCase()] = false;
});

// Spaceship map - 0: floor, 1: wall, 2: door (closed), 3: door (open)
const MAP_WIDTH = 40;
const MAP_HEIGHT = 30;

// Create the spaceship layout
const spaceshipMap = [
    // Row 0-4: Top border
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    
    // Bridge (top room)
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,2,2,2,2,2,2,2,2,2,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    
    // Main corridor
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    
    // Left room (Engine Room)
    [1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,0,0,2,2,0,0,0,0,0,0,0,0,0,2,2,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    
    // Lower corridor and rooms
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    
    // Bottom rooms (Cargo Bay and Living Quarters)
    [1,1,1,0,0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,2,2,2,2,0,0,0,0,0,0,0,0,0,2,2,2,2,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
];

// Door positions for easy reference
const doors = [
    // Bridge doors
    {x: 15, y: 9, isOpen: false},
    {x: 16, y: 9, isOpen: false},
    {x: 17, y: 9, isOpen: false},
    {x: 18, y: 9, isOpen: false},
    {x: 19, y: 9, isOpen: false},
    {x: 20, y: 9, isOpen: false},
    {x: 21, y: 9, isOpen: false},
    {x: 22, y: 9, isOpen: false},
    {x: 23, y: 9, isOpen: false},
    
    // Engine room doors
    {x: 13, y: 15, isOpen: false},
    {x: 14, y: 15, isOpen: false},
    {x: 24, y: 15, isOpen: false},
    {x: 25, y: 15, isOpen: false},
    
    // Cargo bay doors
    {x: 11, y: 22, isOpen: false},
    {x: 12, y: 22, isOpen: false},
    {x: 13, y: 22, isOpen: false},
    {x: 14, y: 22, isOpen: false},
    {x: 25, y: 22, isOpen: false},
    {x: 26, y: 22, isOpen: false},
    {x: 27, y: 22, isOpen: false},
    {x: 28, y: 22, isOpen: false}
];

// Collision detection
function isWall(x, y) {
    const mapX = Math.floor(x / TILE_SIZE);
    const mapY = Math.floor(y / TILE_SIZE);

    if (mapX < 0 || mapX >= MAP_WIDTH || mapY < 0 || mapY >= MAP_HEIGHT) {
        return true;
    }

    const tile = spaceshipMap[mapY][mapX];
    return tile === 1 || tile === 2; // Wall or closed door
}

// Check if player can move to position
function canMoveTo(x, y) {
    // Check all four corners of the player
    const halfSize = PLAYER_SIZE / 2;
    return !isWall(x - halfSize, y - halfSize) &&
           !isWall(x + halfSize, y - halfSize) &&
           !isWall(x - halfSize, y + halfSize) &&
           !isWall(x + halfSize, y + halfSize);
}

// Update door states based on player proximity
function updateDoors() {
    doors.forEach(door => {
        const doorX = door.x * TILE_SIZE + TILE_SIZE / 2;
        const doorY = door.y * TILE_SIZE + TILE_SIZE / 2;

        const distance = Math.sqrt(
            Math.pow(player.x - doorX, 2) +
            Math.pow(player.y - doorY, 2)
        );

        if (distance < DOOR_OPEN_DISTANCE) {
            door.isOpen = true;
            spaceshipMap[door.y][door.x] = 3; // Open door
        } else {
            door.isOpen = false;
            spaceshipMap[door.y][door.x] = 2; // Closed door
        }
    });
}

// Update player position
function updatePlayer() {
    let newX = player.x;
    let newY = player.y;

    if (keys['w']) newY -= player.speed;
    if (keys['s']) newY += player.speed;
    if (keys['a']) newX -= player.speed;
    if (keys['d']) newX += player.speed;

    // Check horizontal movement
    if (canMoveTo(newX, player.y)) {
        player.x = newX;
    }

    // Check vertical movement
    if (canMoveTo(player.x, newY)) {
        player.y = newY;
    }
}

// Render the game
function render() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw map
    for (let y = 0; y < MAP_HEIGHT; y++) {
        for (let x = 0; x < MAP_WIDTH; x++) {
            const tile = spaceshipMap[y][x];
            const drawX = x * TILE_SIZE;
            const drawY = y * TILE_SIZE;

            switch (tile) {
                case 0: // Floor
                    ctx.fillStyle = '#333';
                    break;
                case 1: // Wall
                    ctx.fillStyle = '#666';
                    break;
                case 2: // Closed door
                    ctx.fillStyle = '#ff6600';
                    break;
                case 3: // Open door
                    ctx.fillStyle = '#00ff66';
                    break;
            }

            ctx.fillRect(drawX, drawY, TILE_SIZE, TILE_SIZE);

            // Add some detail to walls
            if (tile === 1) {
                ctx.strokeStyle = '#888';
                ctx.lineWidth = 1;
                ctx.strokeRect(drawX, drawY, TILE_SIZE, TILE_SIZE);
            }
        }
    }

    // Draw player
    ctx.fillStyle = player.color;
    ctx.fillRect(
        player.x - PLAYER_SIZE / 2,
        player.y - PLAYER_SIZE / 2,
        PLAYER_SIZE,
        PLAYER_SIZE
    );

    // Add player border
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.strokeRect(
        player.x - PLAYER_SIZE / 2,
        player.y - PLAYER_SIZE / 2,
        PLAYER_SIZE,
        PLAYER_SIZE
    );
}

// Game loop
function gameLoop() {
    updateDoors();
    updatePlayer();
    render();
    requestAnimationFrame(gameLoop);
}

// Start the game
gameLoop();
